import { apiClient, type ApiResponse, type PaginatedResponse } from './base';

// Widget Management Types
export interface Widget {
  id: number;
  name: string;
  description?: string;
  template: string;
  primary_color: string;
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  welcome_message: string;
  placeholder: string;
  auto_trigger: {
    enabled: boolean;
    delay: number;
    message?: string;
  };
  ai_model: string;
  knowledge_base: string[];
  status: 'active' | 'draft' | 'inactive';
  conversations_count: number;
  created_at: string;
  updated_at: string;
  user_id: number;
}

export interface WidgetTemplate {
  id: string;
  name: string;
  description: string;
  preview_url?: string;
  config: Record<string, any>;
}

export interface WidgetConversation {
  id: number;
  widget_id: number;
  user_identifier: string;
  messages: WidgetMessage[];
  status: 'active' | 'closed' | 'archived';
  satisfaction_rating?: number;
  created_at: string;
  updated_at: string;
}

export interface WidgetMessage {
  id: number;
  conversation_id: number;
  content: string;
  sender: 'user' | 'bot';
  metadata?: Record<string, any>;
  created_at: string;
}

export interface WidgetAnalytics {
  widget_id: number;
  total_conversations: number;
  active_conversations: number;
  avg_response_time: number;
  satisfaction_rate: number;
  popular_queries: Array<{
    query: string;
    count: number;
  }>;
  conversation_trends: Array<{
    date: string;
    count: number;
  }>;
}

// Widget Management API
export const widgetApi = {
  // Widgets CRUD
  getWidgets: (params?: { search?: string; status?: string; page?: number }) =>
    apiClient.getPaginated<Widget>('/widgets', params),
  
  getWidget: (id: number) =>
    apiClient.get<Widget>(`/widgets/${id}`),
  
  createWidget: (data: Partial<Widget>) =>
    apiClient.post<Widget>('/widgets', data),
  
  updateWidget: (id: number, data: Partial<Widget>) =>
    apiClient.put<Widget>(`/widgets/${id}`, data),
  
  deleteWidget: (id: number) =>
    apiClient.delete(`/widgets/${id}`),

  duplicateWidget: (id: number, name: string) =>
    apiClient.post<Widget>(`/widgets/${id}/duplicate`, { name }),

  // Widget Templates
  getTemplates: () =>
    apiClient.get<WidgetTemplate[]>('/widget-templates'),

  getTemplate: (id: string) =>
    apiClient.get<WidgetTemplate>(`/widget-templates/${id}`),

  // Widget Conversations
  getConversations: (widgetId: number, params?: { status?: string; page?: number }) =>
    apiClient.getPaginated<WidgetConversation>(`/widgets/${widgetId}/conversations`, params),

  getConversation: (widgetId: number, conversationId: number) =>
    apiClient.get<WidgetConversation>(`/widgets/${widgetId}/conversations/${conversationId}`),

  closeConversation: (widgetId: number, conversationId: number) =>
    apiClient.put(`/widgets/${widgetId}/conversations/${conversationId}/close`, {}),

  // Widget Analytics
  getWidgetAnalytics: (widgetId: number, params?: { date_range?: string }) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiClient.get<WidgetAnalytics>(`/widgets/${widgetId}/analytics${queryString}`);
  },

  // Widget Testing
  testWidget: (widgetId: number, message: string) =>
    apiClient.post<{ response: string; processing_time: number }>(`/widgets/${widgetId}/test`, { message }),

  // Widget Deployment
  generateEmbedCode: (widgetId: number, options?: { 
    theme?: string; 
    position?: string; 
    auto_open?: boolean;
  }) =>
    apiClient.post<{ embed_code: string; script_url: string }>(`/widgets/${widgetId}/embed`, options || {}),

  // Widget Status
  activateWidget: (id: number) =>
    apiClient.put(`/widgets/${id}/activate`, {}),

  deactivateWidget: (id: number) =>
    apiClient.put(`/widgets/${id}/deactivate`, {}),
};
