import { apiClient, type ApiResponse } from './base';

// Analytics Types
export interface DashboardMetrics {
  widgets_count: number;
  total_conversations: number;
  active_users: number;
  avg_response_time: string;
  satisfaction_rate: string;
  recent_activity: RecentActivity[];
}

export interface RecentActivity {
  id: number;
  type: 'conversation' | 'config' | 'knowledge' | 'user' | 'widget';
  widget?: string;
  time: string;
  user: string;
  description: string;
}

export interface AnalyticsMetrics {
  total_conversations: number;
  active_users: number;
  avg_response_time: string;
  satisfaction_rate: string;
  period_start: string;
  period_end: string;
  conversation_trends: Array<{
    date: string;
    conversations: number;
    users: number;
  }>;
  top_widgets: Array<{
    widget_id: number;
    widget_name: string;
    conversations: number;
    satisfaction: number;
  }>;
  response_time_trends: Array<{
    date: string;
    avg_time: number;
  }>;
  user_engagement: Array<{
    hour: number;
    conversations: number;
  }>;
}

export interface ConversationAnalytics {
  total_conversations: number;
  completed_conversations: number;
  abandoned_conversations: number;
  avg_conversation_length: number;
  avg_messages_per_conversation: number;
  conversation_outcomes: Array<{
    outcome: string;
    count: number;
    percentage: number;
  }>;
  popular_topics: Array<{
    topic: string;
    count: number;
    sentiment: 'positive' | 'neutral' | 'negative';
  }>;
}

export interface UserAnalytics {
  total_users: number;
  new_users: number;
  returning_users: number;
  user_locations: Array<{
    country: string;
    count: number;
  }>;
  user_devices: Array<{
    device: string;
    count: number;
  }>;
  user_satisfaction: Array<{
    rating: number;
    count: number;
  }>;
}

export interface PerformanceAnalytics {
  avg_response_time: number;
  uptime_percentage: number;
  error_rate: number;
  api_calls_count: number;
  cache_hit_rate: number;
  performance_trends: Array<{
    date: string;
    response_time: number;
    error_rate: number;
  }>;
  system_health: {
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    active_connections: number;
  };
}

// Analytics API
export const analyticsApi = {
  // Dashboard Metrics
  getDashboardMetrics: () =>
    apiClient.get<DashboardMetrics>('/dashboard/metrics'),

  // General Analytics
  getAnalytics: (params?: { 
    widget_id?: number; 
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return apiClient.get<AnalyticsMetrics>(`/analytics${queryString}`);
  },

  // Conversation Analytics
  getConversationAnalytics: (params?: {
    widget_id?: number;
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return apiClient.get<ConversationAnalytics>(`/analytics/conversations${queryString}`);
  },

  // User Analytics
  getUserAnalytics: (params?: {
    widget_id?: number;
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return apiClient.get<UserAnalytics>(`/analytics/users${queryString}`);
  },

  // Performance Analytics
  getPerformanceAnalytics: (params?: {
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return apiClient.get<PerformanceAnalytics>(`/analytics/performance${queryString}`);
  },

  // Export Analytics
  exportAnalytics: (params: {
    type: 'conversations' | 'users' | 'performance' | 'overview';
    format: 'csv' | 'xlsx' | 'pdf';
    widget_id?: number;
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const queryString = '?' + new URLSearchParams(params as any).toString();
    return apiClient.get(`/analytics/export${queryString}`);
  },

  // Real-time Analytics
  getRealtimeMetrics: () =>
    apiClient.get<{
      active_conversations: number;
      online_users: number;
      messages_per_minute: number;
      response_time: number;
    }>('/analytics/realtime'),
};
