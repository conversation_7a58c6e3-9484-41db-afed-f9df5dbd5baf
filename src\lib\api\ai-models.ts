import { apiClient, type ApiResponse, type PaginatedResponse } from './base';

// AI Models Types
export interface AIModel {
  id: number;
  name: string;
  provider: 'openai' | 'anthropic' | 'google' | 'azure' | 'custom';
  model: string;
  api_key: string;
  temperature: number;
  max_tokens: number;
  system_prompt?: string;
  status: 'active' | 'inactive' | 'testing';
  is_default: boolean;
  usage_count: number;
  last_used?: string;
  cost_per_token?: number;
  created_at: string;
  updated_at: string;
  created_by: number;
}

export interface AIProvider {
  id: string;
  name: string;
  description: string;
  supported_models: AIProviderModel[];
  authentication_type: 'api_key' | 'oauth' | 'custom';
  documentation_url: string;
  pricing_url?: string;
}

export interface AIProviderModel {
  id: string;
  name: string;
  description: string;
  max_tokens: number;
  supports_streaming: boolean;
  supports_functions: boolean;
  cost_per_1k_tokens: {
    input: number;
    output: number;
  };
}

export interface AIModelTestResult {
  success: boolean;
  response: string;
  processing_time: number;
  tokens_used: {
    input: number;
    output: number;
    total: number;
  };
  cost_estimate: number;
  error_message?: string;
  model_info: {
    provider: string;
    model: string;
    temperature: number;
    max_tokens: number;
  };
}

export interface AIModelUsage {
  model_id: number;
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  total_tokens: number;
  total_cost: number;
  avg_response_time: number;
  usage_trends: Array<{
    date: string;
    requests: number;
    tokens: number;
    cost: number;
  }>;
  popular_prompts: Array<{
    prompt: string;
    count: number;
    avg_tokens: number;
  }>;
}

export interface AIModelPerformance {
  model_id: number;
  avg_response_time: number;
  success_rate: number;
  error_rate: number;
  uptime_percentage: number;
  performance_trends: Array<{
    date: string;
    response_time: number;
    success_rate: number;
  }>;
  error_breakdown: Array<{
    error_type: string;
    count: number;
    percentage: number;
  }>;
}

// AI Models API
export const aiModelsApi = {
  // AI Models CRUD
  getModels: (params?: { 
    search?: string; 
    provider?: string; 
    status?: string;
    page?: number;
  }) =>
    apiClient.getPaginated<AIModel>('/ai-models', params),
  
  getModel: (id: number) =>
    apiClient.get<AIModel>(`/ai-models/${id}`),
  
  createModel: (data: {
    name: string;
    provider: string;
    model: string;
    api_key: string;
    temperature?: number;
    max_tokens?: number;
    system_prompt?: string;
  }) =>
    apiClient.post<AIModel>('/ai-models', data),
  
  updateModel: (id: number, data: Partial<AIModel>) =>
    apiClient.put<AIModel>(`/ai-models/${id}`, data),
  
  deleteModel: (id: number) =>
    apiClient.delete(`/ai-models/${id}`),

  duplicateModel: (id: number, name: string) =>
    apiClient.post<AIModel>(`/ai-models/${id}/duplicate`, { name }),

  // Model Status Management
  activateModel: (id: number) =>
    apiClient.put(`/ai-models/${id}/activate`, {}),

  deactivateModel: (id: number) =>
    apiClient.put(`/ai-models/${id}/deactivate`, {}),

  setAsDefault: (id: number) =>
    apiClient.put(`/ai-models/${id}/set-default`, {}),

  // AI Providers
  getProviders: () =>
    apiClient.get<AIProvider[]>('/ai-models/providers'),

  getProvider: (id: string) =>
    apiClient.get<AIProvider>(`/ai-models/providers/${id}`),

  getProviderModels: (providerId: string) =>
    apiClient.get<AIProviderModel[]>(`/ai-models/providers/${providerId}/models`),

  // Model Testing
  testModel: (id: number, message: string, options?: {
    temperature?: number;
    max_tokens?: number;
    system_prompt?: string;
  }) =>
    apiClient.post<AIModelTestResult>(`/ai-models/${id}/test`, { message, ...options }),

  testModelConfiguration: (config: {
    provider: string;
    model: string;
    api_key: string;
    message: string;
    temperature?: number;
    max_tokens?: number;
    system_prompt?: string;
  }) =>
    apiClient.post<AIModelTestResult>('/ai-models/test-config', config),

  // Model Analytics
  getModelUsage: (id: number, params?: {
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiClient.get<AIModelUsage>(`/ai-models/${id}/usage${queryString}`);
  },

  getModelPerformance: (id: number, params?: {
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiClient.get<AIModelPerformance>(`/ai-models/${id}/performance${queryString}`);
  },

  getAllModelsUsage: (params?: {
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return apiClient.get<AIModelUsage[]>(`/ai-models/usage${queryString}`);
  },

  // Model Validation
  validateApiKey: (provider: string, apiKey: string) =>
    apiClient.post<{ valid: boolean; message: string }>('/ai-models/validate-key', { provider, api_key: apiKey }),

  // Bulk Operations
  bulkActivate: (ids: number[]) =>
    apiClient.put('/ai-models/bulk-activate', { ids }),

  bulkDeactivate: (ids: number[]) =>
    apiClient.put('/ai-models/bulk-deactivate', { ids }),

  bulkDelete: (ids: number[]) =>
    apiClient.delete('/ai-models/bulk-delete', { ids }),
};
