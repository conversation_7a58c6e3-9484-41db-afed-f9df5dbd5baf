import { apiClient, type ApiResponse, type PaginatedResponse } from './base';

// Knowledge Base Types
export interface KnowledgeBaseDocument {
  id: number;
  name: string;
  type: string;
  size: string;
  status: 'processed' | 'processing' | 'failed' | 'pending';
  upload_date: string;
  chunks: number;
  processing_progress?: number;
  error_message?: string;
  metadata?: Record<string, any>;
  created_by: number;
}

export interface KnowledgeBaseWebsite {
  id: number;
  url: string;
  status: 'active' | 'crawling' | 'error' | 'pending';
  last_crawled: string;
  pages: number;
  depth: number;
  crawl_frequency: 'daily' | 'weekly' | 'monthly' | 'manual';
  error_message?: string;
  metadata?: Record<string, any>;
  created_by: number;
}

export interface KnowledgeBaseAPI {
  id: number;
  name: string;
  type: 'REST' | 'GraphQL' | 'SOAP';
  endpoint: string;
  status: 'connected' | 'error' | 'pending';
  last_sync: string;
  sync_frequency: 'realtime' | 'hourly' | 'daily' | 'manual';
  authentication?: {
    type: 'none' | 'api_key' | 'bearer' | 'basic';
    credentials?: Record<string, any>;
  };
  error_message?: string;
  created_by: number;
}

export interface KnowledgeBaseChunk {
  id: number;
  document_id?: number;
  website_id?: number;
  api_id?: number;
  content: string;
  metadata: Record<string, any>;
  embedding_vector?: number[];
  similarity_score?: number;
  created_at: string;
}

export interface KnowledgeBaseTestResult {
  success: boolean;
  message: string;
  results?: Array<{
    source: string;
    content: string;
    similarity: number;
    metadata: Record<string, any>;
  }>;
  processing_time: number;
  total_chunks_searched: number;
}

export interface KnowledgeBaseStats {
  total_documents: number;
  total_websites: number;
  total_apis: number;
  total_chunks: number;
  storage_used: string;
  last_updated: string;
  processing_queue: number;
}

// Knowledge Base API
export const knowledgeBaseApi = {
  // Documents Management
  getDocuments: (params?: { 
    search?: string; 
    status?: string; 
    type?: string;
    page?: number;
  }) =>
    apiClient.getPaginated<KnowledgeBaseDocument>('/knowledge-base/documents', params),
  
  getDocument: (id: number) =>
    apiClient.get<KnowledgeBaseDocument>(`/knowledge-base/documents/${id}`),
  
  uploadDocument: (file: File, metadata?: Record<string, any>) => {
    const formData = new FormData();
    formData.append('file', file);
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }
    return apiClient.post<KnowledgeBaseDocument>('/knowledge-base/documents', formData);
  },
  
  deleteDocument: (id: number) =>
    apiClient.delete(`/knowledge-base/documents/${id}`),

  reprocessDocument: (id: number) =>
    apiClient.post(`/knowledge-base/documents/${id}/reprocess`, {}),

  getDocumentChunks: (id: number, params?: { page?: number }) =>
    apiClient.getPaginated<KnowledgeBaseChunk>(`/knowledge-base/documents/${id}/chunks`, params),

  // Websites Management
  getWebsites: (params?: { 
    search?: string; 
    status?: string; 
    page?: number;
  }) =>
    apiClient.getPaginated<KnowledgeBaseWebsite>('/knowledge-base/websites', params),
  
  getWebsite: (id: number) =>
    apiClient.get<KnowledgeBaseWebsite>(`/knowledge-base/websites/${id}`),
  
  addWebsite: (data: { 
    url: string; 
    depth?: number;
    crawl_frequency?: string;
    metadata?: Record<string, any>;
  }) =>
    apiClient.post<KnowledgeBaseWebsite>('/knowledge-base/websites', data),
  
  updateWebsite: (id: number, data: Partial<KnowledgeBaseWebsite>) =>
    apiClient.put<KnowledgeBaseWebsite>(`/knowledge-base/websites/${id}`, data),
  
  deleteWebsite: (id: number) =>
    apiClient.delete(`/knowledge-base/websites/${id}`),

  crawlWebsite: (id: number) =>
    apiClient.post(`/knowledge-base/websites/${id}/crawl`, {}),

  getWebsitePages: (id: number, params?: { page?: number }) =>
    apiClient.getPaginated<KnowledgeBaseChunk>(`/knowledge-base/websites/${id}/pages`, params),

  // APIs Management
  getAPIs: (params?: { 
    search?: string; 
    status?: string; 
    type?: string;
    page?: number;
  }) =>
    apiClient.getPaginated<KnowledgeBaseAPI>('/knowledge-base/apis', params),
  
  getAPI: (id: number) =>
    apiClient.get<KnowledgeBaseAPI>(`/knowledge-base/apis/${id}`),
  
  addAPI: (data: {
    name: string;
    type: string;
    endpoint: string;
    sync_frequency?: string;
    authentication?: any;
    metadata?: Record<string, any>;
  }) =>
    apiClient.post<KnowledgeBaseAPI>('/knowledge-base/apis', data),
  
  updateAPI: (id: number, data: Partial<KnowledgeBaseAPI>) =>
    apiClient.put<KnowledgeBaseAPI>(`/knowledge-base/apis/${id}`, data),
  
  deleteAPI: (id: number) =>
    apiClient.delete(`/knowledge-base/apis/${id}`),

  syncAPI: (id: number) =>
    apiClient.post(`/knowledge-base/apis/${id}/sync`, {}),

  testAPIConnection: (id: number) =>
    apiClient.post<{ success: boolean; message: string }>(`/knowledge-base/apis/${id}/test`, {}),

  // Knowledge Base Testing & Search
  testKnowledgeBase: (query: string, options?: {
    limit?: number;
    similarity_threshold?: number;
    sources?: string[];
  }) =>
    apiClient.post<KnowledgeBaseTestResult>('/knowledge-base/test', { query, ...options }),

  searchKnowledgeBase: (query: string, params?: {
    limit?: number;
    similarity_threshold?: number;
    sources?: string[];
    page?: number;
  }) =>
    apiClient.post<PaginatedResponse<KnowledgeBaseChunk>>('/knowledge-base/search', { query, ...params }),

  // Knowledge Base Statistics
  getStats: () =>
    apiClient.get<KnowledgeBaseStats>('/knowledge-base/stats'),

  // Bulk Operations
  bulkDelete: (type: 'documents' | 'websites' | 'apis', ids: number[]) =>
    apiClient.delete(`/knowledge-base/${type}/bulk-delete`, { ids }),

  bulkReprocess: (type: 'documents' | 'websites', ids: number[]) =>
    apiClient.post(`/knowledge-base/${type}/bulk-reprocess`, { ids }),
};
