import { useState, useEffect } from 'react';
import { templateApi, type PromptTemplate, handleApiError } from '@/lib/api';
import { toastUtils } from '@/components/ui/use-toast';

export function useTemplates(filters?: { 
  search?: string; 
  category?: string; 
  is_active?: boolean;
  page?: number;
}) {
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await templateApi.getTemplates(filters);
      if (response.success && response.data) {
        setTemplates(response.data);
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      toastUtils.operationError("loading templates", errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = async (data: {
    name: string;
    description: string;
    category: string;
    content: string;
    is_active?: boolean;
  }) => {
    try {
      const response = await templateApi.createTemplate(data);
      if (response.success && response.data) {
        setTemplates(prev => [...prev, response.data!]);
        toastUtils.operationSuccess("Template creation");
        return response.data;
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      toastUtils.operationError("creating template", errorMessage);
      throw err;
    }
  };

  const updateTemplate = async (id: number, data: Partial<PromptTemplate>) => {
    try {
      const response = await templateApi.updateTemplate(id, data);
      if (response.success && response.data) {
        setTemplates(prev => prev.map(t => t.id === id ? response.data! : t));
        toastUtils.operationSuccess("Template update");
        return response.data;
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      toastUtils.operationError("updating template", errorMessage);
      throw err;
    }
  };

  const deleteTemplate = async (id: number) => {
    try {
      await templateApi.deleteTemplate(id);
      setTemplates(prev => prev.filter(t => t.id !== id));
      toastUtils.operationSuccess("Template deletion");
    } catch (err) {
      const errorMessage = handleApiError(err);
      toastUtils.operationError("deleting template", errorMessage);
      throw err;
    }
  };

  const toggleTemplate = async (id: number, active: boolean) => {
    try {
      await templateApi.toggleTemplate(id, active);
      setTemplates(prev => prev.map(t => t.id === id ? { ...t, is_active: active } : t));
      toastUtils.operationSuccess(`Template ${active ? 'activated' : 'deactivated'}`);
    } catch (err) {
      const errorMessage = handleApiError(err);
      toastUtils.operationError("toggling template", errorMessage);
      throw err;
    }
  };

  useEffect(() => {
    loadTemplates();
  }, [filters?.search, filters?.category, filters?.is_active, filters?.page]);

  return {
    templates,
    loading,
    error,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    toggleTemplate,
    refetch: loadTemplates
  };
}
