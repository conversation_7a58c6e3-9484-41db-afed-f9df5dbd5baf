import { apiClient, type ApiResponse, type PaginatedResponse } from './base';

// Prompt Template Types
export interface PromptTemplate {
  id: number;
  name: string;
  description: string;
  category: string;
  content: string;
  is_default: boolean;
  is_active: boolean;
  usage_count: number;
  variables?: TemplateVariable[];
  created_at: string;
  updated_at: string;
  created_by: number;
}

export interface TemplateVariable {
  name: string;
  type: 'text' | 'number' | 'boolean' | 'select';
  description: string;
  required: boolean;
  default_value?: any;
  options?: string[]; // For select type
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  template_count: number;
}

export interface TemplateUsage {
  template_id: number;
  usage_count: number;
  success_rate: number;
  avg_response_time: number;
  last_used: string;
  popular_variables: Array<{
    variable: string;
    value: string;
    count: number;
  }>;
}

export interface TemplateTestResult {
  success: boolean;
  response: string;
  processing_time: number;
  tokens_used: number;
  cost_estimate: number;
  variables_used: Record<string, any>;
}

// Prompt Templates API
export const templateApi = {
  // Templates CRUD
  getTemplates: (params?: { 
    search?: string; 
    category?: string; 
    is_active?: boolean;
    is_default?: boolean;
    page?: number;
  }) =>
    apiClient.getPaginated<PromptTemplate>('/prompt-templates', params),
  
  getTemplate: (id: number) =>
    apiClient.get<PromptTemplate>(`/prompt-templates/${id}`),
  
  createTemplate: (data: {
    name: string;
    description: string;
    category: string;
    content: string;
    variables?: TemplateVariable[];
    is_active?: boolean;
  }) =>
    apiClient.post<PromptTemplate>('/prompt-templates', data),
  
  updateTemplate: (id: number, data: Partial<PromptTemplate>) =>
    apiClient.put<PromptTemplate>(`/prompt-templates/${id}`, data),
  
  deleteTemplate: (id: number) =>
    apiClient.delete(`/prompt-templates/${id}`),

  duplicateTemplate: (id: number, name: string) =>
    apiClient.post<PromptTemplate>(`/prompt-templates/${id}/duplicate`, { name }),

  // Template Status Management
  toggleTemplate: (id: number, active: boolean) =>
    apiClient.put(`/prompt-templates/${id}/toggle`, { is_active: active }),

  setAsDefault: (id: number) =>
    apiClient.put(`/prompt-templates/${id}/set-default`, {}),

  // Template Categories
  getCategories: () =>
    apiClient.get<TemplateCategory[]>('/prompt-templates/categories'),

  createCategory: (data: { name: string; description: string }) =>
    apiClient.post<TemplateCategory>('/prompt-templates/categories', data),

  updateCategory: (id: string, data: { name?: string; description?: string }) =>
    apiClient.put<TemplateCategory>(`/prompt-templates/categories/${id}`, data),

  deleteCategory: (id: string) =>
    apiClient.delete(`/prompt-templates/categories/${id}`),

  // Template Testing
  testTemplate: (id: number, variables?: Record<string, any>) =>
    apiClient.post<TemplateTestResult>(`/prompt-templates/${id}/test`, { variables }),

  testTemplateContent: (content: string, variables?: Record<string, any>) =>
    apiClient.post<TemplateTestResult>('/prompt-templates/test-content', { content, variables }),

  // Template Analytics
  getTemplateUsage: (id: number, params?: {
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiClient.get<TemplateUsage>(`/prompt-templates/${id}/usage${queryString}`);
  },

  getPopularTemplates: (params?: {
    limit?: number;
    category?: string;
    date_range?: string;
  }) => {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return apiClient.get<PromptTemplate[]>(`/prompt-templates/popular${queryString}`);
  },

  // Template Import/Export
  exportTemplate: (id: number, format: 'json' | 'yaml') =>
    apiClient.get(`/prompt-templates/${id}/export?format=${format}`),

  importTemplate: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return apiClient.post<PromptTemplate>('/prompt-templates/import', formData);
  },

  // Bulk Operations
  bulkToggle: (ids: number[], active: boolean) =>
    apiClient.put('/prompt-templates/bulk-toggle', { ids, is_active: active }),

  bulkDelete: (ids: number[]) =>
    apiClient.delete('/prompt-templates/bulk-delete', { ids }),

  bulkUpdateCategory: (ids: number[], category: string) =>
    apiClient.put('/prompt-templates/bulk-category', { ids, category }),
};
