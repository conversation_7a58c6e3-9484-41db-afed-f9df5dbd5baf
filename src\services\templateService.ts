import { templateApi, type PromptTemplate, type Template<PERSON>ategory, handleApiError } from '@/lib/api';

export interface CreateTemplateData {
  name: string;
  description: string;
  category: string;
  content: string;
  is_active?: boolean;
}

export interface UpdateTemplateData extends Partial<CreateTemplateData> {}

export interface TemplateFilters {
  search?: string;
  category?: string;
  is_active?: boolean;
  is_default?: boolean;
  page?: number;
}

class TemplateService {
  async getTemplates(filters?: TemplateFilters) {
    try {
      const response = await templateApi.getTemplates(filters);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch templates');
      }
      return response;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getTemplate(id: number) {
    try {
      const response = await templateApi.getTemplate(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch template');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async createTemplate(data: CreateTemplateData) {
    try {
      const response = await templateApi.createTemplate(data);
      if (!response.success) {
        throw new Error(response.message || 'Failed to create template');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async updateTemplate(id: number, data: UpdateTemplateData) {
    try {
      const response = await templateApi.updateTemplate(id, data);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update template');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async deleteTemplate(id: number) {
    try {
      const response = await templateApi.deleteTemplate(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete template');
      }
      return true;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async duplicateTemplate(id: number, name: string) {
    try {
      const response = await templateApi.duplicateTemplate(id, name);
      if (!response.success) {
        throw new Error(response.message || 'Failed to duplicate template');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async toggleTemplate(id: number, active: boolean) {
    try {
      const response = await templateApi.toggleTemplate(id, active);
      if (!response.success) {
        throw new Error(response.message || 'Failed to toggle template');
      }
      return true;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async setAsDefault(id: number) {
    try {
      const response = await templateApi.setAsDefault(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to set as default');
      }
      return true;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getCategories() {
    try {
      const response = await templateApi.getCategories();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch categories');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async testTemplate(id: number, variables?: Record<string, any>) {
    try {
      const response = await templateApi.testTemplate(id, variables);
      if (!response.success) {
        throw new Error(response.message || 'Failed to test template');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async testTemplateContent(content: string, variables?: Record<string, any>) {
    try {
      const response = await templateApi.testTemplateContent(content, variables);
      if (!response.success) {
        throw new Error(response.message || 'Failed to test template content');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getTemplateUsage(id: number, params?: {
    date_range?: string;
    start_date?: string;
    end_date?: string;
  }) {
    try {
      const response = await templateApi.getTemplateUsage(id, params);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch template usage');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getPopularTemplates(params?: {
    limit?: number;
    category?: string;
    date_range?: string;
  }) {
    try {
      const response = await templateApi.getPopularTemplates(params);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch popular templates');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async bulkToggle(ids: number[], active: boolean) {
    try {
      const response = await templateApi.bulkToggle(ids, active);
      if (!response.success) {
        throw new Error(response.message || 'Failed to bulk toggle templates');
      }
      return true;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async bulkDelete(ids: number[]) {
    try {
      const response = await templateApi.bulkDelete(ids);
      if (!response.success) {
        throw new Error(response.message || 'Failed to bulk delete templates');
      }
      return true;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export const templateService = new TemplateService();
