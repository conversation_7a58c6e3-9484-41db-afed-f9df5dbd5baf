import { useState, useEffect } from 'react';
import { analyticsApi, type DashboardMetrics, handleApiError } from '@/lib/api';
import { toastUtils } from '@/components/ui/use-toast';

export function useDashboard() {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadMetrics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsApi.getDashboardMetrics();
      if (response.success && response.data) {
        setMetrics(response.data);
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      toastUtils.operationError("loading dashboard metrics", errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMetrics();
  }, []);

  return {
    metrics,
    loading,
    error,
    refetch: loadMetrics
  };
}
