import { widgetApi, type Widget, type WidgetAnalytics, handleApiError } from '@/lib/api';

export interface CreateWidgetData {
  name: string;
  description?: string;
  template: string;
  primary_color: string;
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  welcome_message: string;
  placeholder: string;
  auto_trigger: {
    enabled: boolean;
    delay: number;
    message?: string;
  };
  ai_model: string;
  knowledge_base: string[];
}

export interface UpdateWidgetData extends Partial<CreateWidgetData> {
  status?: 'active' | 'draft' | 'inactive';
}

export interface WidgetFilters {
  search?: string;
  status?: string;
  page?: number;
}

class WidgetService {
  async getWidgets(filters?: WidgetFilters) {
    try {
      const response = await widgetApi.getWidgets(filters);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch widgets');
      }
      return response;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getWidget(id: number) {
    try {
      const response = await widgetApi.getWidget(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch widget');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async createWidget(data: CreateWidgetData) {
    try {
      const response = await widgetApi.createWidget(data);
      if (!response.success) {
        throw new Error(response.message || 'Failed to create widget');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async updateWidget(id: number, data: UpdateWidgetData) {
    try {
      const response = await widgetApi.updateWidget(id, data);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update widget');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async deleteWidget(id: number) {
    try {
      const response = await widgetApi.deleteWidget(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete widget');
      }
      return true;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async duplicateWidget(id: number, name: string) {
    try {
      const response = await widgetApi.duplicateWidget(id, name);
      if (!response.success) {
        throw new Error(response.message || 'Failed to duplicate widget');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getWidgetAnalytics(id: number, dateRange?: string) {
    try {
      const response = await widgetApi.getWidgetAnalytics(id, { date_range: dateRange });
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch analytics');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async testWidget(id: number, message: string) {
    try {
      const response = await widgetApi.testWidget(id, message);
      if (!response.success) {
        throw new Error(response.message || 'Failed to test widget');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async activateWidget(id: number) {
    try {
      const response = await widgetApi.activateWidget(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to activate widget');
      }
      return true;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async deactivateWidget(id: number) {
    try {
      const response = await widgetApi.deactivateWidget(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to deactivate widget');
      }
      return true;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async generateEmbedCode(id: number, options?: { 
    theme?: string; 
    position?: string; 
    auto_open?: boolean;
  }) {
    try {
      const response = await widgetApi.generateEmbedCode(id, options);
      if (!response.success) {
        throw new Error(response.message || 'Failed to generate embed code');
      }
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export const widgetService = new WidgetService();
