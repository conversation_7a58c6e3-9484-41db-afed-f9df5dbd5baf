// Central API exports with proper separation of concerns

// Base API client and utilities
export { 
  apiClient, 
  handleApiError, 
  handleValidationErrors,
  type ApiResponse,
  type PaginatedResponse 
} from './base';

// User Management API
export { 
  userApi,
  type User,
  type Role,
  type Permission,
  type UserActivity
} from './user';

// Widget Management API
export { 
  widgetApi,
  type Widget,
  type WidgetTemplate,
  type WidgetConversation,
  type WidgetMessage,
  type WidgetAnalytics
} from './widget';

// Analytics API
export { 
  analyticsApi,
  type DashboardMetrics,
  type RecentActivity,
  type AnalyticsMetrics,
  type ConversationAnalytics,
  type UserAnalytics,
  type PerformanceAnalytics
} from './analytics';

// Prompt Templates API
export { 
  templateApi,
  type PromptTemplate,
  type TemplateVariable,
  type TemplateCategory,
  type TemplateUsage,
  type TemplateTestResult
} from './templates';

// Knowledge Base API
export { 
  knowledgeBaseApi,
  type KnowledgeBaseDocument,
  type KnowledgeBaseWebsite,
  type <PERSON>BaseAPI,
  type <PERSON>Base<PERSON>hunk,
  type KnowledgeBaseTestResult,
  type KnowledgeBaseStats
} from './knowledge-base';

// AI Models API
export { 
  aiModelsApi,
  type AIModel,
  type AIProvider,
  type AIProviderModel,
  type AIModelTestResult,
  type AIModelUsage,
  type AIModelPerformance
} from './ai-models';

// Legacy compatibility - re-export the old combined API
// This maintains backward compatibility while we migrate components
export const legacyApi = {
  // User management (legacy)
  getUsers: userApi.getUsers,
  getUser: userApi.getUser,
  createUser: userApi.createUser,
  updateUser: userApi.updateUser,
  deleteUser: userApi.deleteUser,
  getRoles: userApi.getRoles,
  getRole: userApi.getRole,
  createRole: userApi.createRole,
  updateRole: userApi.updateRole,
  deleteRole: userApi.deleteRole,
  getPermissions: userApi.getPermissions,
  getPermission: userApi.getPermission,
  createPermission: userApi.createPermission,
  updatePermission: userApi.updatePermission,
  deletePermission: userApi.deletePermission,
  assignRole: userApi.assignRole,
  removeRole: userApi.removeRole,
  assignPermission: userApi.assignPermission,
  removePermission: userApi.removePermission,
  assignPermissionsToUser: userApi.assignPermissionsToUser,
  getUserActivities: userApi.getUserActivities,
  exportUserActivities: userApi.exportUserActivities,
};
