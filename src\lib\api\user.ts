import { apiClient, type ApiResponse, type PaginatedResponse } from './base';

// User Management Types
export interface User {
  id: number;
  name: string;
  email: string;
  status: 'active' | 'inactive';
  email_verified_at: string | null;
  created_at: string;
  updated_at: string;
  roles: Role[];
  permissions: Permission[];
}

export interface Role {
  id: number;
  name: string;
  display_name: string;
  description: string | null;
  created_at: string;
  updated_at: string;
  permissions: Permission[];
  users_count?: number;
}

export interface Permission {
  id: number;
  name: string;
  display_name: string;
  description: string | null;
  category: string;
  created_at: string;
  updated_at: string;
}

export interface UserActivity {
  id: number;
  user_id: number;
  action: string;
  description: string;
  ip_address: string;
  user_agent: string;
  status: 'success' | 'failed' | 'warning';
  details: string;
  created_at: string;
  user: User;
}

// User Management API
export const userApi = {
  // Users
  getUsers: (params?: { search?: string; role?: string; status?: string; page?: number }) =>
    apiClient.getPaginated<User>('/users', params),
  
  getUser: (id: number) =>
    apiClient.get<User>(`/users/${id}`),
  
  createUser: (data: { name: string; email: string; password: string; role_ids?: number[] }) =>
    apiClient.post<User>('/users', data),
  
  updateUser: (id: number, data: { name?: string; email?: string; status?: string; role_ids?: number[] }) =>
    apiClient.put<User>(`/users/${id}`, data),
  
  deleteUser: (id: number) =>
    apiClient.delete(`/users/${id}`),
  
  // Roles
  getRoles: (params?: { search?: string; page?: number }) =>
    apiClient.getPaginated<Role>('/roles', params),
  
  getRole: (id: number) =>
    apiClient.get<Role>(`/roles/${id}`),
  
  createRole: (data: { name: string; display_name: string; description: string; permission_ids?: number[] }) =>
    apiClient.post<Role>('/roles', data),
  
  updateRole: (id: number, data: { name?: string; display_name?: string; description?: string; permission_ids?: number[] }) =>
    apiClient.put<Role>(`/roles/${id}`, data),
  
  deleteRole: (id: number) =>
    apiClient.delete(`/roles/${id}`),
  
  // Permissions
  getPermissions: (params?: { search?: string; category?: string; page?: number }) =>
    apiClient.getPaginated<Permission>('/permissions', params),
  
  getPermission: (id: number) =>
    apiClient.get<Permission>(`/permissions/${id}`),
  
  createPermission: (data: { name: string; display_name: string; description: string; category: string }) =>
    apiClient.post<Permission>('/permissions', data),
  
  updatePermission: (id: number, data: { name?: string; display_name?: string; description?: string; category?: string }) =>
    apiClient.put<Permission>(`/permissions/${id}`, data),
  
  deletePermission: (id: number) =>
    apiClient.delete(`/permissions/${id}`),
  
  // Role Assignment
  assignRole: (userId: number, roleId: number) =>
    apiClient.post(`/users/${userId}/roles`, { role_id: roleId }),
  
  removeRole: (userId: number, roleId: number) =>
    apiClient.delete(`/users/${userId}/roles/${roleId}`),
  
  // Permission Assignment
  assignPermission: (userId: number, permissionId: number) =>
    apiClient.post(`/users/${userId}/permissions`, { permission_id: permissionId }),
  
  removePermission: (userId: number, permissionId: number) =>
    apiClient.delete(`/users/${userId}/permissions/${permissionId}`),
  
  assignPermissionsToUser: (userId: number, permissionIds: number[]) =>
    apiClient.post(`/users/${userId}/permissions/bulk`, { permission_ids: permissionIds }),
  
  // User Activity
  getUserActivities: (params?: { 
    search?: string; 
    user_id?: number; 
    action?: string; 
    status?: string;
    date_from?: string;
    date_to?: string;
    page?: number;
  }) =>
    apiClient.getPaginated<UserActivity>('/user-activities', params),
  
  exportUserActivities: (params?: Record<string, any>) =>
    apiClient.get('/user-activities/export', params),
};
