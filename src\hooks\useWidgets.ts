import { useState, useEffect } from 'react';
import { widgetApi, type Widget, handleApiError } from '@/lib/api';
import { toastUtils } from '@/components/ui/use-toast';

export function useWidgets(filters?: { search?: string; status?: string; page?: number }) {
  const [widgets, setWidgets] = useState<Widget[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadWidgets = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await widgetApi.getWidgets(filters);
      if (response.success && response.data) {
        setWidgets(response.data);
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      toastUtils.operationError("loading widgets", errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const createWidget = async (data: Partial<Widget>) => {
    try {
      const response = await widgetApi.createWidget(data);
      if (response.success && response.data) {
        setWidgets(prev => [...prev, response.data!]);
        toastUtils.operationSuccess("Widget creation");
        return response.data;
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      toastUtils.operationError("creating widget", errorMessage);
      throw err;
    }
  };

  const updateWidget = async (id: number, data: Partial<Widget>) => {
    try {
      const response = await widgetApi.updateWidget(id, data);
      if (response.success && response.data) {
        setWidgets(prev => prev.map(w => w.id === id ? response.data! : w));
        toastUtils.operationSuccess("Widget update");
        return response.data;
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      toastUtils.operationError("updating widget", errorMessage);
      throw err;
    }
  };

  const deleteWidget = async (id: number) => {
    try {
      await widgetApi.deleteWidget(id);
      setWidgets(prev => prev.filter(w => w.id !== id));
      toastUtils.operationSuccess("Widget deletion");
    } catch (err) {
      const errorMessage = handleApiError(err);
      toastUtils.operationError("deleting widget", errorMessage);
      throw err;
    }
  };

  useEffect(() => {
    loadWidgets();
  }, [filters?.search, filters?.status, filters?.page]);

  return {
    widgets,
    loading,
    error,
    createWidget,
    updateWidget,
    deleteWidget,
    refetch: loadWidgets
  };
}

export function useWidget(id: number) {
  const [widget, setWidget] = useState<Widget | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadWidget = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await widgetApi.getWidget(id);
        if (response.success && response.data) {
          setWidget(response.data);
        }
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        toastUtils.operationError("loading widget", errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadWidget();
    }
  }, [id]);

  return { widget, loading, error };
}
